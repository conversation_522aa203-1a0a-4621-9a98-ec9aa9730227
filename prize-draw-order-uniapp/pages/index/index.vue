<template>
  <view class="container">
    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- Logo图片 -->
      <view class="logo-container" v-if="logoImageUrl">
        <image :src="logoImageUrl" class="logo-img" mode="aspectFit"></image>
      </view>

      <!-- 商家信息 - 显示在头像下方 -->
      <view class="merchant-info" v-if="merchantInfo">
        <view class="merchant-name">{{ merchantInfo.merchantName }}</view>
        <view class="merchant-address" v-if="merchantInfo.address">{{ merchantInfo.address }}</view>
        <!-- 桌台信息 -->
        <view class="table-info" v-if="tableInfo">
          <view class="table-number">桌台：{{ tableInfo.tableName || tableInfo.tableNumber }}</view>
        </view>
      </view>

      <!-- 活动微信二维码 -->
      <view class="wechat-qrcode" v-if="currentActivity && currentActivity.wechatQrcode">
        <view class="qrcode-title">关注我们</view>
        <image :src="currentActivity.wechatQrcode" class="qrcode-img" mode="aspectFit"></image>
      </view>
    </view>

    <!-- 固定在底部的功能按钮 -->
    <view class="fixed-bottom-buttons">
      <view class="function-btn order-btn" :class="{ 'disabled': !orderButtonEnabled }" @click="goToOrder">
        <view class="btn-icon">🍽️</view>
        <view class="btn-text">
          {{ orderButtonEnabled ? '点餐' : `点餐(${countdown}s)` }}
        </view>
      </view>
      <view class="function-btn lottery-btn" @click="goToLottery">
        <view class="btn-icon">🎁</view>
        <view class="btn-text">抽奖</view>
      </view>
    </view>

    <!-- 欢迎遮罩层 -->
    <view class="welcome-overlay" v-if="showWelcomeOverlay" @click="closeWelcomeOverlay">
      <view class="overlay-content" @click.stop>

        <!-- 图片 -->
        <view class="overlay-image-container" v-if="backgroundImageUrl">
          <span class="close-btn" @click="closeWelcomeOverlay">
            x
          </span>
          <span v-if="welcomeText">{{ welcomeText }}</span>
          <image :src="backgroundImageUrl" class="overlay-image" mode="aspectFit"></image>
          <span> {{ overlayCountdown }}秒后自动关闭</span>
        </view>

      </view>
    </view>

    <!-- 到期提示弹窗 -->
    <u-modal v-model="expiredModal.show" title="系统提示" :show-cancel-button="false" confirm-text="我知道了"
      @confirm="expiredModal.show = false">
      <view class="expired-content">
        <view class="expired-icon">⚠️</view>
        <view class="expired-text">{{ expiredModal.message }}</view>
      </view>
    </u-modal>
  </view>
</template>

<script>
import { merchantApi, tableApi, configApi, lotteryApi, getImageUrl } from '@/utils/api.js'

export default {
  data() {
    return {
      merchantCode: '',
      tableNumber: '',
      merchantInfo: null,
      tableInfo: null,
      merchantConfig: {},
      currentActivity: null,
      expiredModal: {
        show: false,
        message: ''
      },
      // 点餐按钮倒计时相关
      orderButtonEnabled: false,
      countdown: 5,
      countdownTimer: null,
      // 欢迎遮罩层相关
      showWelcomeOverlay: false,
      overlayCountdown: 10,
      overlayTimer: null
    }
  },

  computed: {
    // 解析UI配置
    uiConfig() {
      const uiConfigStr = this.merchantConfig.ui_config
      if (uiConfigStr) {
        try {
          return JSON.parse(uiConfigStr)
        } catch (e) {
          console.error('UI配置解析失败:', e)
          return {}
        }
      }
      return {}
    },

    // 背景图片样式
    backgroundImageStyle() {
      const backgroundImage = this.uiConfig.backgroundImage || this.merchantConfig.scan_page_bg
      return !!backgroundImage
    },

    // 背景图片URL
    backgroundImageUrl() {
      const backgroundImage = this.uiConfig.backgroundImage || this.merchantConfig.scan_page_bg
      if (backgroundImage) {
        return getImageUrl(backgroundImage)
      }
      return ''
    },

    // Logo图片URL
    logoImageUrl() {
      const logoImage = this.uiConfig.logoImage
      return logoImage ? getImageUrl(logoImage) : ''
    },

    // 欢迎语
    welcomeText() {
      return this.uiConfig.welcomeText || ''
    },

    // 页面标题
    pageTitle() {
      return this.uiConfig.pageTitle || '抽奖点餐'
    },

    // 主题色彩
    primaryColor() {
      return this.uiConfig.primaryColor || '#667eea'
    }
  },

  onLoad(options) {
    // 从URL参数或扫码获取商家编码和桌台号
    this.merchantCode = options.merchantCode || '002'
    this.tableNumber = options.tableNumber || 'A002'

    this.initPage()
    this.startOrderButtonCountdown()
  },

  onUnload() {
    // 清除倒计时器
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer)
    }
    if (this.overlayTimer) {
      clearInterval(this.overlayTimer)
    }
  },

  watch: {
    // 监听页面标题变化，动态设置导航栏标题
    pageTitle: {
      handler(newTitle) {
        if (newTitle) {
          uni.setNavigationBarTitle({
            title: newTitle
          })
        }
      },
      immediate: true
    },

    // 监听主题色彩变化，动态设置导航栏颜色
    primaryColor: {
      handler(newColor) {
        if (newColor) {
          uni.setNavigationBarColor({
            frontColor: '#ffffff',
            backgroundColor: newColor
          })
        }
      },
      immediate: true
    }
  },

  methods: {
    async initPage() {
      try {
        // 加载商家信息
        await this.loadMerchantInfo()

        // 加载桌台信息
        if (this.tableNumber) {
          await this.loadTableInfo()
        }

        // 加载商家配置
        await this.loadMerchantConfig()

        // 加载当前活动信息
        await this.loadCurrentActivity()

        // 页面数据加载完成后，延迟显示欢迎遮罩层
        this.$nextTick(() => {
          setTimeout(() => {
            this.showWelcomeOverlayWithTimer()
          }, 500) // 延迟500ms显示，确保页面渲染完成
        })

      } catch (error) {
        console.error('页面初始化失败:', error)
        this.handleError(error)
      }
    },

    async loadMerchantInfo() {
      try {
        const res = await merchantApi.getMerchantInfo(this.merchantCode)
        if (res.code === 200) {
          this.merchantInfo = res.data
        } else {
          throw new Error(res.msg || '获取商家信息失败')
        }
      } catch (error) {
        this.handleError(error)
      }
    },

    async loadTableInfo() {
      try {
        const res = await tableApi.getTableInfo(this.merchantCode, this.tableNumber)
        if (res.code === 200) {
          this.tableInfo = res.data
        } else {
          throw new Error(res.msg || '获取桌台信息失败')
        }
      } catch (error) {
        this.handleError(error)
      }
    },

    async loadMerchantConfig() {
      try {
        const res = await configApi.getAllConfig(this.merchantCode)
        if (res.code === 200) {
          this.merchantConfig = res.data || {}
        }
      } catch (error) {
        console.error('获取商家配置失败:', error)
      }
    },

    async loadCurrentActivity() {
      try {
        const res = await lotteryApi.getCurrentActivity(this.merchantCode)
        if (res.code === 200 && res.data) {
          this.currentActivity = res.data
        }
      } catch (error) {
        console.error('获取活动信息失败:', error)
      }
    },

    // 开始点餐按钮倒计时
    startOrderButtonCountdown() {
      this.orderButtonEnabled = false
      this.countdown = 5

      this.countdownTimer = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          this.orderButtonEnabled = true
          clearInterval(this.countdownTimer)
          this.countdownTimer = null
        }
      }, 1000)
    },

    goToOrder() {
      // 检查按钮是否可用
      if (!this.orderButtonEnabled) {
        uni.showToast({
          title: `请等待 ${this.countdown} 秒后再点击`,
          icon: 'none'
        })
        return
      }

      if (!this.tableInfo || !this.tableInfo.meituanLink) {
        uni.showToast({
          title: '点餐功能暂未开放',
          icon: 'none'
        })
        return
      }

      // 跳转到美团点餐链接
      uni.navigateTo({
        url: `/pages/webview/webview?url=${encodeURIComponent(this.tableInfo.meituanLink)}`
      })
    },

    goToLottery() {
      // 跳转到抽奖页面
      uni.navigateTo({
        url: `/pages/lottery/lottery?merchantCode=${this.merchantCode}&tableNumber=${this.tableNumber}`
      })
    },

    handleError(error) {
      let message = error.message || error.msg || '系统异常'

      // 检查是否是商家到期错误
      if (message.includes('过期') || message.includes('到期')) {
        this.expiredModal.message = message
        this.expiredModal.show = true
      } else {
        uni.showToast({
          title: message,
          icon: 'none',
          duration: 3000
        })
      }
    },

    // 显示欢迎遮罩层并启动倒计时
    showWelcomeOverlayWithTimer() {
      // 只有当有欢迎语或背景图片时才显示遮罩层
      if (this.welcomeText || this.backgroundImageUrl) {
        this.showWelcomeOverlay = true
        this.overlayCountdown = 10

        this.overlayTimer = setInterval(() => {
          this.overlayCountdown--
          if (this.overlayCountdown <= 0) {
            this.closeWelcomeOverlay()
          }
        }, 1000)
      }
    },

    // 关闭欢迎遮罩层
    closeWelcomeOverlay() {
      this.showWelcomeOverlay = false
      if (this.overlayTimer) {
        clearInterval(this.overlayTimer)
        this.overlayTimer = null
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  display: flex;
  flex-direction: column;
  padding-bottom: 160rpx;
  /* 为底部固定按钮留出空间 */
}



// 主要内容区域
.main-content {
  flex: 1;
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  z-index: 2;
  position: relative;
  min-height: 100vh;
}

// Logo容器
.logo-container {
  margin-bottom: 30rpx;

  .logo-img {
    width: 120rpx;
    height: 120rpx;
    border-radius: 60rpx;
    background: rgba(255, 255, 255, 0.9);
    padding: 10rpx;
    box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  }
}

// 商家信息样式 - 显示在头像下方
.merchant-info {
  text-align: center;
  margin: 30rpx 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);

  .merchant-name {
    font-size: 42rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 15rpx;
  }

  .merchant-address {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 15rpx;
  }

  .table-info {
    .table-number {
      font-size: 28rpx;
      color: #888;
      background: rgba(102, 126, 234, 0.1);
      padding: 15rpx 30rpx;
      border-radius: 50rpx;
      display: inline-block;
    }
  }
}



// 固定在底部的功能按钮
.fixed-bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  gap: 20rpx;

  .function-btn {
    flex: 1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 25rpx;
    padding: 30rpx 20rpx;
    text-align: center;
    box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.95);
    }

    &.disabled {
      background: linear-gradient(135deg, #ccc 0%, #999 100%);
      box-shadow: 0 4rpx 15rpx rgba(153, 153, 153, 0.3);

      .btn-text {
        color: #666;
      }
    }

    .btn-icon {
      font-size: 50rpx;
      margin-bottom: 10rpx;
    }

    .btn-text {
      font-size: 28rpx;
      font-weight: bold;
      color: #fff;
    }
  }

  .order-btn {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    box-shadow: 0 4rpx 15rpx rgba(255, 154, 158, 0.3);

    &.disabled {
      background: linear-gradient(135deg, #ccc 0%, #999 100%);
      box-shadow: 0 4rpx 15rpx rgba(153, 153, 153, 0.3);
    }
  }

  .lottery-btn {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    box-shadow: 0 4rpx 15rpx rgba(168, 237, 234, 0.3);
  }
}

// 欢迎遮罩层
.welcome-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 160rpx; // 不遮住底部按钮区域
  background: rgba(0, 0, 0, 0.8); // 恢复黑色遮罩背景
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50; // 降低层级，确保不遮住底部按钮
  padding: 40rpx;

  .overlay-content {
    background: transparent; // 内容区域保持透明
    border-radius: 30rpx;
    padding: 40rpx 30rpx;
    max-width: 600rpx;
    width: 100%;
    text-align: center;
    position: relative;

    .close-btn {
      position: absolute;
      top: -15rpx;
      right: -15rpx;
      width: 60rpx;
      height: 60rpx;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
      backdrop-filter: blur(10rpx);

      &:active {
        transform: scale(0.9);
        background: rgba(255, 255, 255, 1);
      }

    }

    .overlay-welcome-text {
      font-size: 42rpx;
      color: #333;
      font-weight: bold;
      margin-bottom: 30rpx;
      line-height: 1.4;
      background: rgba(255, 255, 255, 0.95);
      padding: 30rpx;
      border-radius: 20rpx;
      box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
      backdrop-filter: blur(10rpx);
    }

    .overlay-image-container {
      margin-bottom: 30rpx;
      background: transparent;
      padding: 20rpx;
      border-radius: 20rpx;
      box-shadow: none;
      backdrop-filter: none;

      .overlay-image {
        width: 100%;
        max-width: 400rpx;
        height: 300rpx;
        border-radius: 15rpx;
        display: block;
      }
    }

    .overlay-countdown {
      font-size: 26rpx;
      color: #666;
      margin-top: 20rpx;
      background: rgba(255, 255, 255, 0.95);
      padding: 15rpx 25rpx;
      border-radius: 20rpx;
      display: inline-block;
      box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
      backdrop-filter: blur(10rpx);
    }
  }
}

.wechat-qrcode {
  text-align: center;
  margin-top: 40rpx;

  .qrcode-title {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 20rpx;
  }

  .qrcode-img {
    width: 250rpx;
    height: 250rpx;
    background: #fff;
    border-radius: 15rpx;
    padding: 15rpx;
    box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  }
}

.expired-content {
  text-align: center;
  padding: 40rpx 20rpx;

  .expired-icon {
    font-size: 80rpx;
    margin-bottom: 30rpx;
  }

  .expired-text {
    font-size: 32rpx;
    color: #666;
    line-height: 1.6;
  }
}
</style>